import 'package:flutter/material.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';
import 'posts_feed.dart';

/// Explore Tab Widget
class ExploreTab extends StatefulWidget {
  final String selectedCategory;

  const ExploreTab({super.key, required this.selectedCategory});

  @override
  State<ExploreTab> createState() => _ExploreTabState();
}

class _ExploreTabState extends State<ExploreTab> {
  final PostService _postService = PostService();
  Post? _topPost;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTopPost();
  }

  @override
  void didUpdateWidget(ExploreTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedCategory != widget.selectedCategory) {
      _loadTopPost();
    }
  }

  Future<void> _loadTopPost() async {
    setState(() {
      _isLoading = true;
    });

    // Ensure PostService is initialized before getting top post
    if (!_postService.isInitialized) {
      await _postService.initialized;
    }

    // Wait a bit more to ensure posts are loaded
    await Future.delayed(const Duration(milliseconds: 100));

    if (mounted) {
      final topPost = _postService.getTopPaidPostForCategory(
        widget.selectedCategory,
      );

      debugPrint(
        'ExploreTab: Loaded top post for category "${widget.selectedCategory}", topPost: ${topPost != null ? "found" : "null"}',
      );

      setState(() {
        _topPost = topPost;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Category Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Icon(Icons.category, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Text(
                widget.selectedCategory,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        // Top Paid Post Container (24-hour system)
        if (_isLoading)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(child: CircularProgressIndicator()),
          )
        else if (_topPost != null)
          TopPaidPostContainer(
            category: widget.selectedCategory,
            topPost: _topPost,
            onTap: () {
              // Navigate to post detail view
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PostDetailScreen(post: _topPost!),
                ),
              );
            },
          ),
        Expanded(
          child: PostsFeed(
            key: ValueKey(widget.selectedCategory),
            category: widget.selectedCategory,
          ),
        ),
      ],
    );
  }
}
